#include "admc_api_wrapper.h"
#include <QDebug>
#include <iostream>
#include <chrono>
#include <iomanip>
#include <sstream>

// 错误码定义（从旧接口移植过来）
#define CMD_SUCCESS                 0
#define CMD_API_ERROR_CHANNEL      -3
#define CMD_API_ERROR_OPEN         -5
#define CMD_API_ERROR_CLOSE        -6
#define CMD_API_ERROR_DSP_BUSY     -7
#define CMD_API_ERROR_OUT_RANGE    -8
#define CMD_API_ERROR_PRM          -11

// 静态实例初始化
AdmcApiWrapper* AdmcApiWrapper::m_instance = nullptr;

AdmcApiWrapper* AdmcApiWrapper::getInstance()
{
    if (m_instance == nullptr) {
        m_instance = new AdmcApiWrapper();
    }
    return m_instance;
}

AdmcApiWrapper::AdmcApiWrapper(QObject* parent)
    : QObject(parent), m_handle(nullptr), m_isConnected(false) // 按照声明顺序初始化成员变量
{
    qDebug() << "ADMC API Wrapper initialized";
    m_handle = API_CreateBoard(); // 在构造函数中创建 handle
    if (m_handle == nullptr) {
        qDebug() << "Error: API_CreateBoard failed!"; // 添加错误日志
        // 可以考虑抛出异常或设置错误状态，具体根据项目需求
    }
}

AdmcApiWrapper::~AdmcApiWrapper()
{
    if (m_isConnected) {
        closeBoard();
    }

    // 删除板卡句柄
    if (m_handle != nullptr) {
        deleteBoard();
    }
}

void __stdcall AdmcApiWrapper::logCallback(LogLevel level, const char* message, void* userData)
{
    AdmcApiWrapper* instance = static_cast<AdmcApiWrapper*>(userData);
    if (!instance) {
        return;
    }

    // 获取当前时间
    auto now = std::chrono::system_clock::now();
    auto in_time_t = std::chrono::system_clock::to_time_t(now);
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()) % 1000;

    std::stringstream time_ss;
    time_ss << std::put_time(std::localtime(&in_time_t), "%Y-%m-%d %X")
            << '.' << std::setfill('0') << std::setw(3) << ms.count();

    QString timestamp = QString::fromStdString(time_ss.str());
    QString ip = instance->m_ip;
    QString msg = QString::fromUtf8(message);
    qDebug() << "logCallback 执行.";
    // 发射结构化信号
    emit instance->logMessage(level, timestamp, ip, msg);
}


short AdmcApiWrapper::openBoard(const QString& ip, int port)
{
    // 如果已连接，先断开
    if (m_isConnected) {
        closeBoard();
    }

    // 确保句柄存在（正常情况下由构造函数创建）
    if (!m_handle) {
        qCritical() << "Board handle is null. Attempting to re-create.";
        m_handle = API_CreateBoard();
        if (!m_handle) {
            qCritical() << "Failed to re-create board handle. Cannot open board.";
            return CMD_API_ERROR_OPEN;
        }
    }

    // 使用已有的句柄打开板卡
    short ret = API_OpenBoard(m_handle, ip.toStdString().c_str(), port);
    if (ret == 0) {
        m_isConnected = true;
        m_ip = ip; // 存储IP
        emit connectionStatusChanged(true);

        // 在板卡成功连接后，立即自动注册日志回调
        registerLogCallback();
        // 默认设置日志级别为INFO
        SetLogLevel(LOG_INFO);

    } else {
        m_isConnected = false;
        m_ip.clear();
        emit errorOccurred(ret, getErrorString(ret));
    }
    return ret;
}

void AdmcApiWrapper::registerLogCallback()
{
    if (m_isConnected && m_handle) {
        API_SetLogCallback(m_handle, AdmcApiWrapper::logCallback, this);
        qDebug() << "Log callback registered.";
    }
}

void AdmcApiWrapper::unregisterLogCallback()
{
    if (m_isConnected && m_handle) {
        API_SetLogCallback(m_handle, nullptr, nullptr);
        qDebug() << "Log callback unregistered.";
    }
}

short AdmcApiWrapper::closeBoard()
{
    if (!m_isConnected) {
        return 0; // 已经断开连接
    }

    // 调用底层API关闭设备
    short result = API_CloseBoard(m_handle);

    if (result == 0) { // 成功断开
        m_isConnected = false;
        m_ip.clear(); // 清除IP
        emit connectionStatusChanged(false);
        qDebug() << "Successfully disconnected from ADMC board";
    } else {
        emit errorOccurred(result, getErrorString(result));
        qDebug() << "Failed to disconnect from ADMC board, error code:" << result;
    }

    return result;
}

void AdmcApiWrapper::deleteBoard()
{
    if (m_handle != nullptr) {
        // 调用底层API删除板卡句柄
        API_DeleteBoard(m_handle);
        m_handle = nullptr;
        qDebug() << "Successfully deleted ADMC board handle";
    }
}

short AdmcApiWrapper::resetBoard()
{
    if (!m_isConnected) {
        return CMD_API_ERROR_CHANNEL; // 未连接错误
    }

    // 调用底层API复位设备
    short result = API_ResetBoard(m_handle);

    if (result != 0) { // 复位失败
        emit errorOccurred(result, getErrorString(result));
        qDebug() << "Failed to reset ADMC board, error code:" << result;
    } else {
        qDebug() << "Successfully reset ADMC board";
    }

    return result;
}

short AdmcApiWrapper::axisOn(short axis)
{
    if (!m_isConnected) {
        return CMD_API_ERROR_CHANNEL; // 未连接错误
    }

    // 调用底层API使能轴
    short result = API_AxisOn(m_handle, axis);

    if (result != 0) { // 使能失败
        emit errorOccurred(result, getErrorString(result));
        qDebug() << "Failed to enable axis" << axis << ", error code:" << result;
    } else {
        qDebug() << "Successfully enabled axis" << axis;
    }

    return result;
}

short AdmcApiWrapper::axisOff(short axis)
{
    if (!m_isConnected) {
        return CMD_API_ERROR_CHANNEL; // 未连接错误
    }

    // 调用底层API禁用轴
    short result = API_AxisOff(m_handle, axis);

    if (result != 0) { // 禁用失败
        emit errorOccurred(result, getErrorString(result));
        qDebug() << "Failed to disable axis" << axis << ", error code:" << result;
    } else {
        qDebug() << "Successfully disabled axis" << axis;
    }

    return result;
}

short AdmcApiWrapper::getAxisPosition(short axis, double& position)
{
    if (!m_isConnected) {
        return CMD_API_ERROR_CHANNEL; // 未连接错误
    }

    // 调用底层API获取轴位置
    short result = API_GetAixsPos(m_handle, axis, position);

    if (result != 0) { // 获取位置失败
        emit errorOccurred(result, getErrorString(result));
        qDebug() << "Failed to get position of axis" << axis << ", error code:" << result;
    } else {
        emit axisPositionChanged(axis, position);
    }

    return result;
}

short AdmcApiWrapper::getCmdaxisPos(int axis, double *pos)
{
    return API_GetCmdAixsPos(m_handle,axis, *pos); // 调用新的底层API
}

short AdmcApiWrapper::getCmdCrdPos(int crd, double *pos)
{
    return API_GetCmdCrdPos(m_handle,crd, pos); // 调用新的底层API
}

short AdmcApiWrapper::getAxisStatus(short axis, short& status)
{
    if (!m_isConnected) {
        return CMD_API_ERROR_CHANNEL; // 未连接错误
    }

    // 调用底层API获取轴状态
    short result = API_GetAxisStatus(m_handle, axis, status);

    if (result != 0) { // 获取状态失败
        emit errorOccurred(result, getErrorString(result));
        qDebug() << "Failed to get status of axis" << axis << ", error code:" << result;
    } else {
        emit axisStatusChanged(axis, status);
    }

    return result;
}

short AdmcApiWrapper::getCrdPos(short crd, double* pPos)
{
    if (!m_isConnected) {
        return CMD_API_ERROR_CHANNEL; // 未连接错误
    }

    // 调用底层API获取坐标系位置
    short result = API_GetCrdPos(m_handle, crd, pPos);

    if (result != 0) { // 获取位置失败
        emit errorOccurred(result, getErrorString(result));
        qDebug() << "Failed to get position of coordinate system" << crd << ", error code:" << result;
    } else {
        //qDebug() << "Successfully got position of coordinate system" << crd << ": X=" << pPos[0] << ", Y=" << pPos[1];
    }

    return result;
}

short AdmcApiWrapper::setJogMode(short crd)
{
    if (!m_isConnected) {
        return CMD_API_ERROR_CHANNEL; // 未连接错误
    }

    // 调用底层API设置Jog模式
    short result = API_SetJogMode(m_handle, crd);
    if (result != 0) { // 设置失败
        emit errorOccurred(result, getErrorString(result));
        qDebug() << "Failed to set Jog mode for crd" << crd << ", error code:" << result;
    } else {
        qDebug() << "Successfully set Jog mode for crd" << crd;
    }

    return result;
}

short AdmcApiWrapper::setJogParameters(short crd, int32_t Maxvel, int32_t acc, int32_t dec, int32_t rate)
{
    if (!m_isConnected) {
        return CMD_API_ERROR_CHANNEL; // 未连接错误
    }

    // 调用底层API设置Jog参数 - 新接口直接传递参数
    short result = API_SetJogPrm(m_handle, crd, Maxvel, acc, dec, rate);

    if (result != 0) { // 设置参数失败
        emit errorOccurred(result, getErrorString(result));
        qDebug() << "Failed to set Jog parameters for crd" << crd << ", error code:" << result;
    } else {
        qDebug() << "Successfully set Jog parameters for crd" << crd
                 << " - Maxvel:" << Maxvel << " acc:" << acc << " dec:" << dec << " rate:" << rate;
    }

    return result;
}

short AdmcApiWrapper::jogUpdate(short axis, short dir)
{
    if (!m_isConnected) {
        return CMD_API_ERROR_CHANNEL; // 未连接错误
    }

    // 调用底层API执行Jog运动

    short result = API_JogUpdate(m_handle, axis, dir);

    if (result != 0) { // Jog运动失败
        emit errorOccurred(result, getErrorString(result));
        qDebug() << "Failed to update Jog for axis" << axis << "with direction" << dir << ", error code:" << result;
    } else {
        qDebug() << "Successfully updated Jog for axis" << axis << "with direction" << dir;
    }

    return result;
}

short AdmcApiWrapper::setCrdTrapMode(short crd)
{
    if (!m_isConnected) {
        return CMD_API_ERROR_CHANNEL; // 未连接错误
    }

    // 调用底层API设置坐标系点位模式
short result = API_SetCrdTrapMode(m_handle, crd);

    if (result != 0) { // 设置失败
        emit errorOccurred(result, getErrorString(result));
        qDebug() << "Failed to set Trap mode for crd" << crd << ", error code:" << result;
    } else {
        qDebug() << "Successfully set Trap mode for crd" << crd;
    }

    return result;
}

short AdmcApiWrapper::setCrdTrapParameters(short crd, double posTarget[2], double velMax, double acc, short rat)
{
    if (!m_isConnected) {
        return CMD_API_ERROR_CHANNEL; // 未连接错误
    }

    // 调用底层API设置坐标系点位参数 - 新接口直接传递参数
    short result = API_SetCrdTrapPrm(m_handle, crd, posTarget, velMax, acc, rat);

    if (result != 0) { // 设置参数失败
        emit errorOccurred(result, getErrorString(result));
        qDebug() << "Failed to set Trap parameters for crd" << crd << ", error code:" << result;
    } else {
        qDebug() << "Successfully set Trap parameters for crd" << crd
                 << " posTarget:[" << posTarget[0] << "," << posTarget[1] << "]"
                 << " velMax:" << velMax << " acc:" << acc << " rat:" << rat;
    }

    return result;
}

short AdmcApiWrapper::crdTrapUpdate(short crd)
{
    if (!m_isConnected) {
        return CMD_API_ERROR_CHANNEL; // 未连接错误
    }

    // 调用底层API更新坐标系点位运动
    short result = API_CrdTrapUpdate(m_handle, crd);

    if (result != 0) { // 更新失败
        emit errorOccurred(result, getErrorString(result));
        qDebug() << "Failed to update Trap for crd" << crd << ", error code:" << result;
    } else {
        qDebug() << "Successfully updated Trap for crd" << crd;
    }

    return result;
}

short AdmcApiWrapper::setAxisTrapMode(short axis)
{
    if (!m_isConnected) {
        return CMD_API_ERROR_CHANNEL; // 未连接错误
    }

    // 调用底层API设置轴点位模式 - 新接口只需要轴号
    short result = API_SetAxisTrapMode(m_handle, axis);

    if (result != 0) { // 设置失败
        emit errorOccurred(result, getErrorString(result));
        qDebug() << "Failed to set Axis Trap mode for axis" << axis << ", error code:" << result;
    } else {
        qDebug() << "Successfully set Axis Trap mode for axis" << axis;
    }

    return result;
}

short AdmcApiWrapper::setAxisTrapParameters(short axis, double IncrPos, double velMax, double acc, short rat)
{
    if (!m_isConnected) {
        return CMD_API_ERROR_CHANNEL; // 未连接错误
    }

    // 调用底层API设置轴点位参数 - 新接口直接传递参数
    short result = API_SetAxisTrapPrm(m_handle, axis, IncrPos, velMax, acc, rat);

    if (result != 0) { // 设置参数失败
        emit errorOccurred(result, getErrorString(result));
        qDebug() << "Failed to set Axis Trap parameters for axis" << axis << ", error code:" << result;
    } else {
        qDebug() << "Successfully set Axis Trap parameters for axis" << axis
                 << " - IncrPos:" << IncrPos << " velMax:" << velMax
                 << " acc:" << acc << " rat:" << rat;
    }

    return result;
}

short AdmcApiWrapper::axisTrapUpdate(short axis)
{
    if (!m_isConnected) {
        return CMD_API_ERROR_CHANNEL; // 未连接错误
    }

    // 调用底层API更新轴点位运动
    // 注意：新接口中API_AxisTrapUpdate的参数仍然是crd，但实际应该传入对应的坐标系
    // axis = [0,1]时 对应crd = 0  axis = [2,3]时 对应crd = 1
    short crd = axis / 2;
    short result = API_AxisTrapUpdate(m_handle, crd);

    if (result != 0) { // 更新失败
        emit errorOccurred(result, getErrorString(result));
        qDebug() << "Failed to update Axis Trap for axis" << axis << "(crd" << crd << "), error code:" << result;
    } else {
        qDebug() << "Successfully updated Axis Trap for axis" << axis << "(crd" << crd << ")";
    }

    return result;
}

short AdmcApiWrapper::axisGoHome(short axis)
{
    if (!m_isConnected) {
        return CMD_API_ERROR_CHANNEL; // 未连接错误
    }

    // 调用底层API执行回零
    short result = API_AxisGoHome(m_handle, axis);

    if (result != 0) { // 回零失败
        emit errorOccurred(result, getErrorString(result));
        qDebug() << "Failed to go home for axis" << axis << ", error code:" << result;
    } else {
        qDebug() << "Successfully started home operation for axis" << axis;
    }

    return result;
}

short AdmcApiWrapper::axisClearAlarm(short axis)
{
    if (!m_isConnected) {
        return CMD_API_ERROR_CHANNEL; // 未连接错误
    }

    // 调用底层API清除报警
    short result = API_AxisClearAlarm(m_handle, axis);

    if (result != 0) { // 清除报警失败
        emit errorOccurred(result, getErrorString(result));
        qDebug() << "Failed to clear alarm for axis" << axis << ", error code:" << result;
    } else {
        qDebug() << "Successfully cleared alarm for axis" << axis;
    }

    return result;
}

short AdmcApiWrapper::getAxisErrorCode(short axis, int& errorCode)
{
    if (!m_isConnected) {
        return CMD_API_ERROR_CHANNEL; // 未连接错误
    }
    unsigned short TempError = 0;
    // 调用底层API获取轴错误码 - 新接口移除了crd参数
    short result = API_GetErrorCode(m_handle, axis, &TempError);
    errorCode = TempError;

    if (result != 0) { // 获取错误码失败
        emit errorOccurred(result, getErrorString(result));
        qDebug() << "Failed to get error code for axis" << axis << ", error code:" << result;
    } else {
        qDebug() << "Successfully got error code for axis" << axis << ": " << errorCode;
    }

    return result;
}

QString AdmcApiWrapper::getErrorString(int errorCode)
{
    // 根据错误码返回对应的错误信息
    switch (errorCode) {
        case CMD_SUCCESS:
            return "操作成功";
        case CMD_API_ERROR_CHANNEL:
            return "通道错误，设备未连接";
        case CMD_API_ERROR_OPEN:
            return "打开设备失败";
        case CMD_API_ERROR_CLOSE:
            return "关闭设备失败";
        case CMD_API_ERROR_DSP_BUSY:
            return "DSP忙，请稍后再试";
        case CMD_API_ERROR_OUT_RANGE:
            return "数据范围超出限制";
        case CMD_API_ERROR_PRM:
            return "参数错误";


        default:
            // 对于正数错误码，尝试使用API_GetServoErrorString获取伺服驱动器错误描述
            if (errorCode > 0 && m_handle != nullptr) {
                wchar_t errorBuffer[256] = {0}; // 创建宽字符缓冲区
                // API_GetServoErrorString现在支持int类型参数，直接传入
                short result = API_GetServoErrorString(m_handle, errorCode, errorBuffer, 256);

                if (result == CMD_SUCCESS) {
                    // 将宽字符转换为QString并返回
                    return QString::fromWCharArray(errorBuffer);
                }
                // 如果API调用失败，继续使用默认处理
            }

            // 默认错误处理
            if (errorCode < 0) {
                return QString("DSP错误: %1").arg(errorCode);
            } else {
                return QString("未知错误: 0x%1").arg(errorCode, 4, 16, QChar('0')).toUpper();
            }
    }
}


// 新增：坐标系参数设置
short AdmcApiWrapper::setCrdPrm(short crd, double synVelMax, double synAccMax)
{
    if (!m_isConnected || m_handle == nullptr) {
        qWarning() << "setCrdPrm called while not connected or handle is null.";
        emit errorOccurred(CMD_API_ERROR_CHANNEL, getErrorString(CMD_API_ERROR_CHANNEL));
        return CMD_API_ERROR_CHANNEL; // 未连接错误
    }

    // 调用底层API设置坐标系参数 - 新接口直接传递参数
    short result = API_SetCrdPrm(m_handle, crd, synVelMax, synAccMax);

    if (result != 0) { // 设置失败
        emit errorOccurred(result, getErrorString(result));
        qDebug() << "Failed to set Crd parameters for crd" << crd << ", error code:" << result;
    } else {
        qDebug() << "Successfully set Crd parameters for crd" << crd
                 << " - synVelMax:" << synVelMax << " synAccMax:" << synAccMax;
    }

    return result;
}

// 新增：直线插补
short AdmcApiWrapper::ln(
    short crd,
    int32_t x,
    int32_t y,
    double synVel,
    double synAcc,
    double velEnd
)
{
    // 标记未使用的参数以消除编译警告 - 新接口已移除velEnd参数但保持接口兼容性
    (void)velEnd;

    if (!m_isConnected || m_handle == nullptr) {
        qWarning() << "ln called while not connected or handle is null.";
        emit errorOccurred(CMD_API_ERROR_CHANNEL, getErrorString(CMD_API_ERROR_CHANNEL));
        return CMD_API_ERROR_CHANNEL; // 未连接错误
    }

    // 调用底层API执行直线插补 - 新接口移除了velEnd参数
    short result = API_Ln(m_handle, crd, x, y, synVel, synAcc);

    if (result != 0) {
        emit errorOccurred(result, getErrorString(result));
        qDebug() << "Failed to execute Ln command for crd" << crd << ", error code:" << result;
    } else {
        qDebug() << "Successfully executed Ln command for crd" << crd
                 << " - x:" << x << " y:" << y << " synVel:" << synVel << " synAcc:" << synAcc;
    }

    return result;
}

// 新增：圆弧插补
short AdmcApiWrapper::ArcXYR(
    short crd,
    int32_t x,
    int32_t y,
    double radius,
    short circleDir,
    double synVel,
    double synAcc,
    double velEnd
)
{
    // 标记未使用的参数以消除编译警告 - 新接口已移除velEnd参数但保持接口兼容性
    (void)velEnd;

    if (!m_isConnected || m_handle == nullptr) {
        qWarning() << "ArcXYR called while not connected or handle is null.";
        emit errorOccurred(CMD_API_ERROR_CHANNEL, getErrorString(CMD_API_ERROR_CHANNEL));
        return CMD_API_ERROR_CHANNEL; // 未连接错误
    }

    // 调用底层API执行圆弧插补 - 新接口移除了velEnd参数
    short result = API_ArcXYR(m_handle, crd, x, y, radius, circleDir, synVel, synAcc);

    if (result != 0) {
        emit errorOccurred(result, getErrorString(result));
        qDebug() << "Failed to execute ArcXYR command for crd" << crd << ", error code:" << result;
    } else {
        qDebug() << "Successfully executed ArcXYR command for crd" << crd
                 << " - x:" << x << " y:" << y << " radius:" << radius
                 << " circleDir:" << circleDir << " synVel:" << synVel << " synAcc:" << synAcc;
    }

    return result;
}

// 新增：三点圆弧插补
short AdmcApiWrapper::ArcXY_3point(
    short crd,
    int32_t* p1,
    int32_t* p2,
    int32_t* p3,
    double synVel,
    double synAcc
)
{
    if (!m_isConnected || m_handle == nullptr) {
        qWarning() << "ArcXY_3point called while not connected or handle is null.";
        emit errorOccurred(CMD_API_ERROR_CHANNEL, getErrorString(CMD_API_ERROR_CHANNEL));
        return CMD_API_ERROR_CHANNEL; // 未连接错误
    }

    // 调用底层API执行三点圆弧插补（新接口移除了radius和circleDir参数）
    short result = API_ArcXY_3point(m_handle, crd, p1, p2, p3, synVel, synAcc);

    if (result != 0) {
        emit errorOccurred(result, getErrorString(result));
        qDebug() << "Failed to execute ArcXY_3point command for crd" << crd << ", error code:" << result;
    } else {
        qDebug() << "Successfully executed ArcXY_3point command for crd" << crd
                 << " - p1:[" << p1[0] << "," << p1[1] << "] p2:[" << p2[0] << "," << p2[1] << "] p3:[" << p3[0] << "," << p3[1] << "]"
                 << " synVel:" << synVel << " synAcc:" << synAcc;
    }

    return result;
}

// 新增：中心圆弧插补
short AdmcApiWrapper::ArcXYC(
    short crd,
    int32_t x,
    int32_t y,
    double xCenter,
    double yCenter,
    short circleDir,
    double synVel,
    double synAcc
)
{
    if (!m_isConnected || m_handle == nullptr) {
        qWarning() << "ArcXYC called while not connected or handle is null.";
        emit errorOccurred(CMD_API_ERROR_CHANNEL, getErrorString(CMD_API_ERROR_CHANNEL));
        return CMD_API_ERROR_CHANNEL; // 未连接错误
    }

    // 调用底层API执行中心圆弧插补
    short result = API_ArcXYC(m_handle, crd, x, y, xCenter, yCenter, circleDir, synVel, synAcc);

    if (result != 0) {
        emit errorOccurred(result, getErrorString(result));
        qDebug() << "Failed to execute ArcXYC command for crd" << crd << ", error code:" << result;
    } else {
        qDebug() << "Successfully executed ArcXYC command for crd" << crd
                 << " - x:" << x << " y:" << y << " xCenter:" << xCenter << " yCenter:" << yCenter
                 << " circleDir:" << circleDir << " synVel:" << synVel << " synAcc:" << synAcc;
    }

    return result;
}

// 新增：启动坐标系插补运动
short AdmcApiWrapper::crdStart(short crd)
{
    if (!m_isConnected || m_handle == nullptr) {
        qWarning() << "crdStart called while not connected or handle is null.";
        emit errorOccurred(CMD_API_ERROR_CHANNEL, getErrorString(CMD_API_ERROR_CHANNEL));
        return CMD_API_ERROR_CHANNEL; // 未连接错误
    }

    // 调用底层API启动坐标系插补运动
    short result = API_CrdStart(m_handle, crd);

    if (result != 0) {
        emit errorOccurred(result, getErrorString(result));
        qDebug() << "Failed to start coordinate system" << crd << ", error code:" << result;
    } else {
        qDebug() << "Successfully started coordinate system" << crd;
    }

    return result;
}

// 急停坐标系插补运动
short AdmcApiWrapper::crdStop(short crd)
{
    if (!m_isConnected || m_handle == nullptr) {
        qWarning() << "crdStop called while not connected or handle is null.";
        emit errorOccurred(CMD_API_ERROR_CHANNEL, getErrorString(CMD_API_ERROR_CHANNEL));
        return CMD_API_ERROR_CHANNEL; // 未连接错误
    }

    // 调用底层API急停坐标系插补运动
    short result = API_CrdStop(m_handle,crd);

    if (result != 0) {
        emit errorOccurred(result, getErrorString(result));
        qDebug() << "Failed to emergency stop coordinate system" << crd << ", error code:" << result;
    } else {
        qDebug() << "Successfully emergency stopped coordinate system" << crd;
    }

    return result;
}

// 暂停坐标系插补运动
short AdmcApiWrapper::crdPause(short crd)
{
    if (!m_isConnected || m_handle == nullptr) {
        qWarning() << "crdPause called while not connected or handle is null.";
        emit errorOccurred(CMD_API_ERROR_CHANNEL, getErrorString(CMD_API_ERROR_CHANNEL));
        return CMD_API_ERROR_CHANNEL; // 未连接错误
    }

    // 调用底层API暂停坐标系插补运动
    short result = API_CrdPause(m_handle,crd);

    if (result != 0) {
        emit errorOccurred(result, getErrorString(result));
        qDebug() << "Failed to pause coordinate system" << crd << ", error code:" << result;
    } else {
        qDebug() << "Successfully paused coordinate system" << crd;
    }

    return result;
}

// 新增：轴参数初始化
short AdmcApiWrapper::setAxisPrm(short crd, short* axisMap, short* axisDir, int32_t* velMax, int32_t* accMax, int32_t* positive, int32_t* negative)
{
    if (!m_isConnected || m_handle == nullptr) {
        qWarning() << "setAxisPrm called while not connected or handle is null.";
        emit errorOccurred(CMD_API_ERROR_CHANNEL, getErrorString(CMD_API_ERROR_CHANNEL));
        return CMD_API_ERROR_CHANNEL; // 未连接错误
    }

    // 调用底层API设置轴参数
    short result = API_SetAxisPrm(m_handle, crd, axisMap, axisDir, velMax, accMax, positive, negative);

    if (result != 0) {
        emit errorOccurred(result, getErrorString(result));
        qDebug() << "Failed to set Axis parameters for crd" << crd << ", error code:" << result;
    } else {
        qDebug() << "Successfully set Axis parameters for crd" << crd;
    }

    return result;
}

// 新增：坐标系轴映射
short AdmcApiWrapper::setAxisMapping(short x1, short y1, short x2, short y2)
{
    if (!m_isConnected || m_handle == nullptr) {
        qWarning() << "setAxisMapping called while not connected or handle is null.";
        emit errorOccurred(CMD_API_ERROR_CHANNEL, getErrorString(CMD_API_ERROR_CHANNEL));
        return CMD_API_ERROR_CHANNEL; // 未连接错误
    }

    // 注意：新接口中可能没有API_AxisMapping函数，轴映射功能可能已整合到API_SetAxisPrm中
    // 这里暂时返回成功，实际的轴映射应该通过setAxisPrm方法来设置
    qWarning() << "setAxisMapping: 新接口中可能没有独立的轴映射函数，请使用setAxisPrm方法";
    qDebug() << "Axis mapping parameters: x1=" << x1 << ", y1=" << y1 << ", x2=" << x2 << ", y2=" << y2;

    // 返回成功，但实际功能需要通过其他方式实现
    return 0;
}

// 新增：设置设备输出
short AdmcApiWrapper::setDeviceOutput(int* deviceOutput)
{
    if (!m_isConnected || m_handle == nullptr) {
        qWarning() << "setDeviceOutput called while not connected or handle is null.";
        emit errorOccurred(CMD_API_ERROR_CHANNEL, getErrorString(CMD_API_ERROR_CHANNEL));
        return CMD_API_ERROR_CHANNEL; // 未连接错误
    }

    // 调用底层API设置设备输出
    short result = API_SetDeviceOutput(m_handle, deviceOutput);

    if (result != 0) {
        emit errorOccurred(result, getErrorString(result));
        qDebug() << "Failed to set device output, error code:" << result;
        qDebug() << "API_SetDeviceOutput:" << deviceOutput;
    } else {
        qDebug() << "Successfully set device output";
    }

    return result;
}
short AdmcApiWrapper::getDeviceOutput(int* deviceOutput)
{
    if (!m_isConnected || m_handle == nullptr) {
        qWarning() << "getDeviceOutput called while not connected or handle is null.";
        emit errorOccurred(CMD_API_ERROR_CHANNEL, getErrorString(CMD_API_ERROR_CHANNEL));
        return CMD_API_ERROR_CHANNEL; // 未连接错误
    }

    // 调用底层API设置设备输出
    short result = API_GetDeviceOutput(m_handle, deviceOutput);

    if (result != 0) {
        emit errorOccurred(result, getErrorString(result));
        qDebug() << "Failed to Get device output, error code:" << result;
        qDebug() << "API_GetDeviceOutput:" << deviceOutput;
    } else {
        //qDebug() << "Successfully Get device output";
    }

    return result;
}
// 新增：获取设备输入
short AdmcApiWrapper::getDeviceInput(int32_t* deviceInput)
{
    if (!m_isConnected || m_handle == nullptr) {
        qWarning() << "getDeviceInput called while not connected or handle is null.";
        emit errorOccurred(CMD_API_ERROR_CHANNEL, getErrorString(CMD_API_ERROR_CHANNEL));
        return CMD_API_ERROR_CHANNEL; // 未连接错误
    }

    // 调用底层API获取设备输入
    short result = API_GetDeviceInput(m_handle, deviceInput);

    if (result != 0) {
        emit errorOccurred(result, getErrorString(result));
        qDebug() << "Failed to get device input, error code:" << result;
    }

    return result;
}

short AdmcApiWrapper::SetLogLevel(LogLevel minLevel)
{
    if (!m_isConnected || m_handle == nullptr) {
        qWarning() << "SetLogLevel called while not connected or handle is null.";
        emit errorOccurred(CMD_API_ERROR_CHANNEL, getErrorString(CMD_API_ERROR_CHANNEL));
        return CMD_API_ERROR_CHANNEL; // 未连接错误
    }

    // 调用底层API设置日志级别
    short result = API_SetLogLevel(m_handle, minLevel);

    if (result != 0) {
        emit errorOccurred(result, getErrorString(result));
        qDebug() << "Failed to set log level, error code:" << result;
    } else {
        qDebug() << "Successfully set log level to" << static_cast<int>(minLevel);
    }

    return result;
}
