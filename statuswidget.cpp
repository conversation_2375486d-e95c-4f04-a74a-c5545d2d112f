#include "statuswidget.h"
#include "ui_statuswidget.h"
#include <QMessageBox>
#include <QFileDialog>
#include <QTextStream>
#include <QDateTime>
#include <QStandardItemModel>
#include <QHeaderView>
#include "admc_api_wrapper.h"

// 初始化静态成员变量
StatusWidget* StatusWidget::m_instance = nullptr;

// 获取单例实例
StatusWidget* StatusWidget::getInstance()
{
    return m_instance;
}

StatusWidget::StatusWidget(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::StatusWidget),
    m_refreshTimer(new QTimer(this)),
    m_apiWrapper(AdmcApiWrapper::getInstance()),
    m_unitConverter(UnitConverter::getInstance())
{
    // 设置单例实例
    m_instance = this;
    ui->setupUi(this);

    // 设置表格头
    QStringList headers;
    headers << "轴号" << "使能" << "正限位" << "负限位" << "原点" << "到位" << "运动中" << "报警" << "急停" << "复位中" << "角度识别" << "报警码";
    ui->tableStatus->setColumnCount(headers.size());
    ui->tableStatus->setHorizontalHeaderLabels(headers);
    ui->tableStatus->setRowCount(4); // 假设有4个轴

    // 设置各列宽度
    ui->tableStatus->setColumnWidth(0, 60);  // 轴号
    for (int i = 1; i < headers.size() - 1; ++i) {
        ui->tableStatus->setColumnWidth(i, 60); // 状态位
    }
    ui->tableStatus->setColumnWidth(headers.size() - 1, 80); // 报警码

    // 设置表格行高，使图标能够正常显示
    ui->tableStatus->verticalHeader()->setDefaultSectionSize(32);

    // 设置表格图标大小
    ui->tableStatus->setIconSize(QSize(24, 24));

    // 连接信号槽
    connect(ui->btnRefresh, &QPushButton::clicked, this, &StatusWidget::refreshStatus);
    connect(ui->btnClear, &QPushButton::clicked, this, &StatusWidget::clearStatus);
    connect(ui->btnExport, &QPushButton::clicked, this, &StatusWidget::exportStatus);
    connect(ui->radioRealtime, &QRadioButton::toggled, this, &StatusWidget::updateDisplayMode);

    // 连接单位转换器的信号
    connect(m_unitConverter, &UnitConverter::unitTypeChanged, this, &StatusWidget::onUnitTypeChanged);

    // 设置自动刷新定时器
    connect(m_refreshTimer, &QTimer::timeout, this, &StatusWidget::refreshStatus);

    // 初始化数据
    refreshStatus();
    updateDisplayMode();
}

StatusWidget::~StatusWidget()
{
    m_refreshTimer->stop();
    delete ui;
}

void StatusWidget::refreshStatus()
{
    updateStatusTable();

    // 更新最后刷新时间
    ui->labelLastUpdate->setText("最后更新时间: " + QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss"));
}

void StatusWidget::clearStatus()
{
    // 清空表格数据
    for (int row = 0; row < ui->tableStatus->rowCount(); ++row) {
        // 保留轴号
        QTableWidgetItem *axisItem = new QTableWidgetItem(QString::number(row));
        axisItem->setTextAlignment(Qt::AlignCenter);
        ui->tableStatus->setItem(row, 0, axisItem);

        // 清空状态位和报警码
        for (int col = 1; col < ui->tableStatus->columnCount(); ++col) {
            QTableWidgetItem *item = new QTableWidgetItem();
            item->setTextAlignment(Qt::AlignCenter);

            // 对于状态位列，设置灰色LED图标
            if (col < ui->tableStatus->columnCount() - 1) {
                item->setIcon(QIcon(":/Resources/LedNone.ico"));
            }

            ui->tableStatus->setItem(row, col, item);
        }

        // 发送报警状态变化信号，清除报警状态
        emit axisAlarmStatusChanged(row, false);
    }

    ui->labelLastUpdate->setText("已清除所有状态数据");
}

void StatusWidget::exportStatus()
{
    QString fileName = QFileDialog::getSaveFileName(this, "导出状态数据",
                                                   "轴状态_" + QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss") + ".csv",
                                                   "CSV文件 (*.csv);;所有文件 (*.*)");
    if (fileName.isEmpty()) {
        return;
    }

    QFile file(fileName);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        QMessageBox::warning(this, "导出失败", "无法写入文件: " + fileName);
        return;
    }

    QTextStream out(&file);

    // 写入表头
    QStringList headers;
    for (int col = 0; col < ui->tableStatus->columnCount(); ++col) {
        headers << ui->tableStatus->horizontalHeaderItem(col)->text();
    }
    out << headers.join(",") << "\n";

    // 写入数据
    for (int row = 0; row < ui->tableStatus->rowCount(); ++row) {
        QStringList rowData;
        for (int col = 0; col < ui->tableStatus->columnCount(); ++col) {
            rowData << ui->tableStatus->item(row, col)->text();
        }
        out << rowData.join(",") << "\n";
    }

    file.close();
    QMessageBox::information(this, "导出成功", "轴状态数据已成功导出到: " + fileName);
}

void StatusWidget::updateDisplayMode()
{
    if (ui->radioRealtime->isChecked()) {
        // 启动实时更新
        m_refreshTimer->start(1000); // 每秒更新一次
        ui->btnRefresh->setEnabled(false);
    } else {
        // 停止实时更新
        m_refreshTimer->stop();
        ui->btnRefresh->setEnabled(true);
    }
}

void StatusWidget::updateStatusTable()
{
    // 只有在连接状态下才获取轴状态
    if (m_apiWrapper && m_apiWrapper->isConnected()) {
        for (int row = 0; row < ui->tableStatus->rowCount(); ++row) {
            short axis = row;
            short status = 0;

            // 轴号
            QTableWidgetItem *axisItem = new QTableWidgetItem(QString::number(axis));
            axisItem->setTextAlignment(Qt::AlignCenter);
            ui->tableStatus->setItem(row, 0, axisItem);

            // 获取轴状态
            short result = m_apiWrapper->getAxisStatus(axis, status);

            if (result == 0) {
                // 获取成功，解析状态位
                bool hasAlarm = false;

                // 存储轴状态
                m_axisStatus[axis] = status;

                // 处理每一个状态位
                for (int bit = 0; bit < 10; ++bit) {
                    bool isSet = (status & (1 << bit)) != 0;
                    QTableWidgetItem *bitItem = new QTableWidgetItem();
                    bitItem->setTextAlignment(Qt::AlignCenter);

                    // 设置图标
                    if (isSet) {
                        // 使用蓝色LED图标表示状态为ON
                        bitItem->setIcon(QIcon(":/Resources/LedGreen.ico"));
                    } else {
                        // 使用灰色LED图标表示状态为OFF
                        bitItem->setIcon(QIcon(":/Resources/LedNone.ico"));
                    }

                    // 检查是否有报警（ALM位）
                    if (bit == 6 && isSet) {
                        hasAlarm = true;
                    }

                    ui->tableStatus->setItem(row, bit + 1, bitItem);
                }

                // 存储报警状态
                m_axisAlarmStatus[axis] = hasAlarm;

                // 报警码列
                QTableWidgetItem *errorCodeItem = new QTableWidgetItem();
                errorCodeItem->setTextAlignment(Qt::AlignCenter);

                // 如果有报警，获取报警码
                if (hasAlarm) {
                    int errorCode = 0;
                    short apiResult = m_apiWrapper->getAxisErrorCode(axis, errorCode);

                    if (apiResult == 0) {
                        // API调用成功
                        if (errorCode != 0) {
                            // 有错误码，显示错误信息
                            // errorCode现在是int类型，可以正确处理大的错误码值
                            long alarmCode = static_cast<long>(errorCode);
                            m_axisAlarmCode[axis] = alarmCode;

                            // 使用AdmcApiWrapper的getErrorString获取官方错误描述
                            QString errorDescription = m_apiWrapper->getErrorString(static_cast<unsigned short>(errorCode));

                            // 格式化为 "0xCODE: Description"
                            QString alarmText = QString("0x%1: %2")
                                .arg(alarmCode, 4, 16, QChar('0')).toUpper()
                                .arg(errorDescription);

                            errorCodeItem->setText(alarmText);
                            errorCodeItem->setBackground(Qt::red);
                            errorCodeItem->setForeground(Qt::white);
                            emit axisAlarmCodeChanged(axis, alarmCode);
                        } else {
                            // 错误码为0，表示无错误
                            if (m_axisAlarmCode.contains(axis)) {
                                m_axisAlarmCode.remove(axis);
                                emit axisAlarmCodeChanged(axis, 0);
                            }
                            errorCodeItem->setText("");
                        }
                    } else {
                        // API调用失败
                        if (m_axisAlarmCode.contains(axis)) {
                             m_axisAlarmCode.remove(axis);
                             emit axisAlarmCodeChanged(axis, 0);
                        }
                        // 显示API调用失败的原因
                        QString apiErrorMsg = m_apiWrapper->getErrorString(apiResult);
                        errorCodeItem->setText(QString("获取失败: %1").arg(apiErrorMsg));
                        errorCodeItem->setBackground(Qt::yellow);
                        errorCodeItem->setForeground(Qt::black);
                    }
                } else {
                    // 如果之前有报警码，现在没有了，也需要清除
                    if (m_axisAlarmCode.contains(axis)) {
                        m_axisAlarmCode.remove(axis);
                        emit axisAlarmCodeChanged(axis, 0);
                    }
                }

                ui->tableStatus->setItem(row, ui->tableStatus->columnCount() - 1, errorCodeItem);

                // 获取轴位置
                double position = 0.0;
                result = m_apiWrapper->getAxisPosition(axis, position);
                if (result == 0) {
                    // 存储轴位置
                    m_axisPosition[axis] = position;
                    // 发送轴位置变化信号
                    // 如果当前单位是mm，需要转换位置值
                    if (m_unitConverter->getCurrentUnitType() == UNIT_MM) {
                        emit axisPositionChanged(axis, m_unitConverter->pulseToMm(position));
                    } else {
                        emit axisPositionChanged(axis, position);
                    }
                }

                // 发送轴状态变化信号
                emit axisStatusChanged(axis, status);

                // 发送报警状态变化信号
                emit axisAlarmStatusChanged(axis, hasAlarm);
            } else {
                // 获取失败，清空所有状态位
                for (int bit = 1; bit < ui->tableStatus->columnCount(); ++bit) {
                    QTableWidgetItem *bitItem = new QTableWidgetItem();
                    bitItem->setTextAlignment(Qt::AlignCenter);

                    // 对于状态位列，设置灰色LED图标
                    if (bit < ui->tableStatus->columnCount() - 1) {
                        bitItem->setIcon(QIcon(":/Resources/LedNone.ico"));
                    }

                    ui->tableStatus->setItem(row, bit, bitItem);
                }

                // 清除存储的状态
                m_axisStatus.remove(axis);
                m_axisAlarmStatus[axis] = false;

                // 发送报警状态变化信号，清除报警状态
                emit axisAlarmStatusChanged(axis, false);
            }
        }
    } else {
        // 未连接状态下清空所有数据
        for (int row = 0; row < ui->tableStatus->rowCount(); ++row) {
            short axis = row;

            // 轴号
            QTableWidgetItem *axisItem = new QTableWidgetItem(QString::number(axis));
            axisItem->setTextAlignment(Qt::AlignCenter);
            ui->tableStatus->setItem(row, 0, axisItem);

            // 清空状态位
            for (int bit = 1; bit < ui->tableStatus->columnCount(); ++bit) {
                QTableWidgetItem *bitItem = new QTableWidgetItem();
                bitItem->setTextAlignment(Qt::AlignCenter);

                // 对于状态位列，设置灰色LED图标
                if (bit < ui->tableStatus->columnCount() - 1) {
                    bitItem->setIcon(QIcon(":/Resources/LedNone.ico"));
                }

                ui->tableStatus->setItem(row, bit, bitItem);
            }

            // 清除存储的状态
            m_axisStatus.remove(axis);
            m_axisAlarmStatus[axis] = false;
            m_axisPosition.remove(axis);

            // 发送报警状态变化信号，清除报警状态
            emit axisAlarmStatusChanged(axis, false);
        }
    }
}

QString StatusWidget::getStatusBitDescription(short bit)
{
    switch (bit) {
        case 0: return "ENA"; // 使能
        case 1: return "PEL"; // 正限位信号
        case 2: return "NEL"; // 负限位信号
        case 3: return "ORG"; // 原点信号
        case 4: return "INP"; // 到位信号
        case 5: return "MOV"; // 移动信号
        case 6: return "ALM"; // 报警信号
        case 7: return "EMG"; // 急停信号
        case 8: return "RST"; // 复位中
        case 9: return "ANG"; // 角度识别
        default: return "";
    }
}

QColor StatusWidget::getStatusBitColor(bool isSet)
{
    // 这个函数不再使用，但保留以避免修改其他代码
    return isSet ? Qt::green : Qt::transparent;
}

// 获取轴状态
short StatusWidget::getAxisStatus(short axis, short& status) const
{
    if (m_axisStatus.contains(axis)) {
        status = m_axisStatus[axis];
        return 0; // 成功
    }
    return -1; // 失败
}

// 检查轴是否有报警
bool StatusWidget::hasAxisAlarm(short axis) const
{
    return m_axisAlarmStatus.value(axis, false);
}

long StatusWidget::getAxisAlarmCode(short axis) const
{
    return m_axisAlarmCode.value(axis, 0L);
}

// 获取轴位置
double StatusWidget::getAxisPosition(short axis) const
{
    double position = m_axisPosition.value(axis, 0.0);

    // 如果当前单位是mm，需要转换位置值
    if (m_unitConverter->getCurrentUnitType() == UNIT_MM) {
        return m_unitConverter->pulseToMm(position);
    }

    return position;
}

// 获取轴使能状态
bool StatusWidget::isAxisEnabled(short axis) const
{
    if (!m_axisStatus.contains(axis)) {
        return false;
    }

    // 使能状态位是Bit 0
    return (m_axisStatus[axis] & (1 << 0)) != 0;
}

// 检查轴是否在原点
bool StatusWidget::isAxisAtOrigin(short axis) const
{
    if (!m_axisStatus.contains(axis)) {
        return false;
    }

    // 原点信号位是Bit 3
    return (m_axisStatus[axis] & (1 << 3)) != 0;
}

// 检查轴是否在复位中
bool StatusWidget::isAxisResetting(short axis) const
{
    if (!m_axisStatus.contains(axis)) {
        return false;
    }

    // 复位中位是Bit 8
    return (m_axisStatus[axis] & (1 << 8)) != 0;
}

// 单位类型变化响应
void StatusWidget::onUnitTypeChanged(UnitType /*type*/)
{
    // 当单位类型变化时刷新状态显示
    refreshStatus();
}

// 注意：initializeAlarmCodeMap 和 getAlarmDescription 函数已删除
// 现在使用 AdmcApiWrapper::getErrorString 获取官方错误描述
